"""CodeStandardMCP - 编码规范即服务"""

__version__ = "1.1.0"
__author__ = "CodeStandardMCP"
__description__ = "编码规范服务工具，提供规范查询、合规检查和管理功能的MCP服务器"

from .config.settings import config
from .models.standard import StandardSelector, StandardDocument, StandardMetadata
from .models.tenant import TenantData, TenantRequest
from .models.session import SessionData, SessionRequest
from .models.comment import CommentData, CommentRequest
from .services.standard_fetcher import standard_fetcher
from .services.standards_manager import standards_manager
from .services.tenant_service import tenant_service
from .services.session_service import session_service
from .services.comment_service import comment_service
from .storage.database_manager import database_manager
from .utils.git_utils import git_utils

__all__ = [
    "config",
    "StandardSelector",
    "StandardDocument",
    "StandardMetadata",
    "TenantData",
    "TenantRequest",
    "SessionData",
    "SessionRequest",
    "CommentData",
    "CommentRequest",
    "standard_fetcher",
    "standards_manager",
    "tenant_service",
    "session_service",
    "comment_service",
    "database_manager",
    "git_utils",
]
