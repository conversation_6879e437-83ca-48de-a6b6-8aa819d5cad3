"""
评价数据模型

定义评价相关的数据结构和验证规则
"""

import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, ConfigDict


class CommentData(BaseModel):
    """评价数据模型"""
    comment_id: str = Field(..., description="评价id")
    session_id: str = Field(..., description="会话标识")
    comment: Optional[str] = Field(None, description="用户评价文字")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    uploaded_at: Optional[datetime] = Field(None, description="上传时间（为空表示未上传）")

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def generate_comment_id(cls) -> str:
        """生成唯一的评价ID"""
        return str(uuid.uuid4())

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "comment_id": self.comment_id,
            "session_id": self.session_id,
            "comment": self.comment,
            "created_at": self.created_at.isoformat(),
            "uploaded_at": self.uploaded_at.isoformat() if self.uploaded_at else None
        }


class CommentRequest(BaseModel):
    """评价创建请求模型"""
    session_id: str = Field(..., description="会话ID")
    comment: Optional[str] = Field(None, description="用户评价文字")

    def to_comment_data(self) -> CommentData:
        """转换为CommentData"""
        return CommentData(
            comment_id=CommentData.generate_comment_id(),
            session_id=self.session_id,
            comment=self.comment
        )
