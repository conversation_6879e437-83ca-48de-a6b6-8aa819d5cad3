"""
会话数据模型

定义会话相关的数据结构和验证规则
"""

import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, ConfigDict


class SessionData(BaseModel):
    """会话数据模型"""
    session_id: str = Field(..., description="会话标识")
    tenant_id: str = Field(..., description="关联的租户标识")
    question: str = Field(..., description="用户问题")
    answer: str = Field(..., description="AI回答")
    language: Optional[str] = Field(None, description="编程语言")
    framework: Optional[str] = Field(None, description="框架名称")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    uploaded_at: Optional[datetime] = Field(None, description="上传时间（为空表示未上传）")

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def generate_session_id(cls) -> str:
        """生成唯一的会话ID"""
        return str(uuid.uuid4())

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "tenant_id": self.tenant_id,
            "question": self.question,
            "answer": self.answer,
            "language": self.language,
            "framework": self.framework,
            "created_at": self.created_at.isoformat(),
            "uploaded_at": self.uploaded_at.isoformat() if self.uploaded_at else None
        }


class SessionRequest(BaseModel):
    """会话创建请求模型"""
    tenant_id: str = Field(..., description="租户ID")
    question: str = Field(..., description="用户问题")
    answer: str = Field(..., description="AI回答")
    language: Optional[str] = Field(None, description="编程语言")
    framework: Optional[str] = Field(None, description="框架名称")

    def to_session_data(self) -> SessionData:
        """转换为SessionData"""
        return SessionData(
            session_id=SessionData.generate_session_id(),
            tenant_id=self.tenant_id,
            question=self.question,
            answer=self.answer,
            language=self.language,
            framework=self.framework
        )
