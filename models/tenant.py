"""
租户数据模型

定义租户相关的数据结构和验证规则
"""

import uuid
from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field, ConfigDict


class TenantData(BaseModel):
    """租户数据模型"""
    tenant_id: str = Field(..., description="租户唯一标识")
    user_id: str = Field(..., description="用户ID")
    name: str = Field(..., description="租户名称")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    uploaded_at: Optional[datetime] = Field(None, description="上传时间（为空表示未上传）")

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def generate_tenant_id(cls) -> str:
        """生成唯一的租户ID"""
        return str(uuid.uuid4())

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "tenant_id": self.tenant_id,
            "user_id": self.user_id,
            "name": self.name,
            "created_at": self.created_at.isoformat(),
            "uploaded_at": self.uploaded_at.isoformat() if self.uploaded_at else None
        }


class TenantRequest(BaseModel):
    """租户创建请求模型"""
    user_id: str = Field(..., description="用户ID")
    name: str = Field(..., description="租户名称")

    def to_tenant_data(self) -> TenantData:
        """转换为TenantData"""
        return TenantData(
            tenant_id=TenantData.generate_tenant_id(),
            user_id=self.user_id,
            name=self.name
        )
