# 环境变量配置说明

## 数据库配置

### 本地数据库路径
```bash
# 设置本地SQLite数据库文件路径
export CSMCP_LOCAL_DB_PATH="/path/to/your/database.db"

# Windows示例
set CSMCP_LOCAL_DB_PATH=D:\data\csmcp\database.db

# 默认值：./data/feedback.db
```

### 远程数据库路径
```bash
# 设置远程SQLite数据库文件路径（可选）
export CSMCP_REMOTE_DB_PATH="/path/to/remote/database.db"

# Windows示例
set CSMCP_REMOTE_DB_PATH=\\server\share\database.db

# 默认值：未设置（不使用远程数据库）
```

## 租户配置

### 自动创建租户
```bash
# 是否自动创建租户
export CSMCP_TENANT_AUTO_CREATE=true

# 默认值：true
```

### 默认租户名
```bash
# 默认租户名称
export CSMCP_DEFAULT_TENANT_NAME=default

# 默认值：unknown
```

## 会话记录配置

### 自动记录会话
```bash
# 是否自动记录fetch_standard调用的会话
export CSMCP_AUTO_RECORD_SESSIONS=true

# 默认值：true
```

## API配置

### 用户ID
```bash
# API调用的用户ID
export CSMCP_API_USER_ID=55

# 默认值：55
```

### API地址
```bash
# 编码规范API地址
export CSMCP_API_URL=http://10.12.135.167:9090/api/local_doc_qa/local_doc_chat

# 默认值：http://10.12.135.167:9090/api/local_doc_qa/local_doc_chat
```

## 数据目录配置

### 数据目录
```bash
# 数据存储根目录
export CSMCP_DATA_DIR=./data

# 默认值：./data
```

## 完整配置示例

### Linux/Mac
```bash
#!/bin/bash
export CSMCP_LOCAL_DB_PATH="/home/<USER>/csmcp/database.db"
export CSMCP_REMOTE_DB_PATH="/mnt/shared/csmcp/database.db"
export CSMCP_TENANT_AUTO_CREATE=true
export CSMCP_AUTO_RECORD_SESSIONS=true
export CSMCP_API_USER_ID=100
export CSMCP_DATA_DIR="/home/<USER>/csmcp/data"

# 启动服务
python main.py
```

### Windows
```batch
@echo off
set CSMCP_LOCAL_DB_PATH=D:\csmcp\database.db
set CSMCP_REMOTE_DB_PATH=\\server\share\csmcp\database.db
set CSMCP_TENANT_AUTO_CREATE=true
set CSMCP_AUTO_RECORD_SESSIONS=true
set CSMCP_API_USER_ID=100
set CSMCP_DATA_DIR=D:\csmcp\data

REM 启动服务
python main.py
```

## 数据库文件说明

### 数据库表结构
- **tenants**: 租户信息表
- **sessions**: 会话记录表（对话表）
- **comments**: 评价表
- **feedback**: 原有反馈表（向后兼容）
- **qa_sessions**: 原有会话表（向后兼容）

### 数据库文件位置
- 默认位置：`./data/feedback.db`
- 可通过 `CSMCP_LOCAL_DB_PATH` 环境变量自定义
- 启动时会在日志中显示实际使用的数据库文件路径

### 数据库初始化
- 首次启动时自动创建数据库文件和表结构
- 支持数据库结构升级和向后兼容
- 如果数据库文件不存在，会自动创建
