## 1. 对话 (Session)

| 字段名         | 类型       | 必填 | 说明            |
|:------------| :--------- | :--- |:--------------|
| session_id  | UUID       | 是   | 会话标识          |
| tenant_id   | UUID       | 是   | 关联的租户标识       |
| question    | Text       | 是   | 用户问题          |
| answer      | Text       | 是   | AI回答          |
| language    | String(50) | 否   | 编程语言          |
| framework   | String(50) | 否   | 框架名称          |
| created_at  | DateTime   | 是   | 创建时间          |
| uploaded_at | DateTime   | 否   | 上传时间（为空表示未上传） |



## 2.评价(Comment)

| 字段名     | 类型     | 必填 | 说明                         |
| :--------- | :------- | :--- | :--------------------------- |
| comment_id | UUID     | 是   | 评价id                       |
| session_id | UUID     | 是   | 会话标识                     |
| comment    | Text     | 否   | 用户评价文字                 |
| created_at | DateTime | 是   | 创建时间                     |
| uploaded_at | DateTime | 否   | 上传时间（为空表示未上传） |



## 3. 租户 (Tenant)

| 字段名     | 类型        | 必填 | 说明                         |
| :--------- | :---------- | :--- | :--------------------------- |
| tenant_id  | UUID        | 是   | 租户唯一标识                 |
| user_id    | String(255) | 是   | 用户id                       |
| name       | String(255) | 是   | 租户名称                     |
| created_at | DateTime    | 是   | 创建时间                     |
| uploaded_at | DateTime | 否   | 上传时间（为空表示未上传） |

其中name是通过git config --global user.email获取@前面的一部分获取

userid则是外部配置中的CSMCP_API_USER_ID