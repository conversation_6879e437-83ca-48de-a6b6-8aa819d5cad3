"""
会话服务

提供会话的存储、查询和管理功能
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from models.session import SessionData, SessionRequest
from storage.database_manager import database_manager

logger = logging.getLogger("CodeStandardMCP.SessionService")


class SessionService:
    """会话服务类"""

    def __init__(self):
        pass

    async def initialize(self):
        """初始化会话服务"""
        try:
            success = await database_manager.initialize_databases()
            if success:
                logger.info("会话服务初始化成功")
            else:
                logger.warning("会话服务初始化部分失败")
            return success
        except Exception as e:
            logger.error(f"会话服务初始化失败: {e}")
            return False

    async def create_session(self, session_request: SessionRequest) -> SessionData:
        """创建会话"""
        try:
            session_data = session_request.to_session_data()
            await self._save_session(session_data)
            logger.info(f"会话创建成功: {session_data.session_id}")
            return session_data
        except Exception as e:
            logger.error(f"创建会话失败: {e}")
            raise

    async def _save_session(self, session_data: SessionData):
        """保存会话到数据库"""
        sql = """
        INSERT INTO sessions (
            session_id, tenant_id, question, answer, language, framework, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            session_data.session_id,
            session_data.tenant_id,
            session_data.question,
            session_data.answer,
            session_data.language,
            session_data.framework,
            session_data.created_at.isoformat()
        )
        
        await database_manager.execute_insert(sql, params, use_remote=False)

    async def get_session(self, session_id: str) -> Optional[SessionData]:
        """根据session_id获取会话数据"""
        try:
            sql = "SELECT * FROM sessions WHERE session_id = ?"
            rows = await database_manager.execute_query(sql, (session_id,), use_remote=False)
            
            if not rows:
                logger.warning(f"未找到会话: {session_id}")
                return None

            row = rows[0]
            return SessionData(
                session_id=row['session_id'],
                tenant_id=row['tenant_id'],
                question=row['question'],
                answer=row['answer'],
                language=row['language'],
                framework=row['framework'],
                created_at=datetime.fromisoformat(row['created_at']),
                uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None
            )

        except Exception as e:
            logger.error(f"获取会话数据失败: {e}")
            return None

    async def get_recent_sessions(self, tenant_id: str, limit: int = 10) -> List[SessionData]:
        """获取租户的最近会话"""
        try:
            sql = """
            SELECT * FROM sessions 
            WHERE tenant_id = ? 
            ORDER BY created_at DESC 
            LIMIT ?
            """
            rows = await database_manager.execute_query(sql, (tenant_id, limit), use_remote=False)
            
            sessions = []
            for row in rows:
                session = SessionData(
                    session_id=row['session_id'],
                    tenant_id=row['tenant_id'],
                    question=row['question'],
                    answer=row['answer'],
                    language=row['language'],
                    framework=row['framework'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None
                )
                sessions.append(session)
            
            return sessions

        except Exception as e:
            logger.error(f"获取最近会话失败: {e}")
            return []

    async def get_latest_session(self, tenant_id: str) -> Optional[SessionData]:
        """获取租户的最新会话"""
        try:
            sessions = await self.get_recent_sessions(tenant_id, limit=1)
            return sessions[0] if sessions else None
        except Exception as e:
            logger.error(f"获取最新会话失败: {e}")
            return None

    async def get_session_stats(self, tenant_id: str) -> Dict[str, Any]:
        """获取会话统计信息"""
        try:
            # 基础统计
            stats_sql = """
            SELECT 
                COUNT(*) as total_sessions,
                COUNT(DISTINCT language) as unique_languages,
                COUNT(DISTINCT framework) as unique_frameworks,
                MAX(created_at) as latest_session
            FROM sessions
            WHERE tenant_id = ?
            """
            
            stats_result = await database_manager.execute_query(stats_sql, (tenant_id,), use_remote=False)
            stats_row = stats_result[0] if stats_result else {}

            # 语言分布
            language_sql = """
            SELECT language, COUNT(*) as count 
            FROM sessions 
            WHERE tenant_id = ? AND language IS NOT NULL 
            GROUP BY language 
            ORDER BY count DESC
            """
            language_result = await database_manager.execute_query(language_sql, (tenant_id,), use_remote=False)
            language_distribution = {row['language']: row['count'] for row in language_result}

            return {
                "total_sessions": stats_row.get('total_sessions', 0),
                "unique_languages": stats_row.get('unique_languages', 0),
                "unique_frameworks": stats_row.get('unique_frameworks', 0),
                "latest_session": stats_row.get('latest_session'),
                "language_distribution": language_distribution
            }

        except Exception as e:
            logger.error(f"获取会话统计失败: {e}")
            return {
                "total_sessions": 0,
                "unique_languages": 0,
                "unique_frameworks": 0,
                "latest_session": None,
                "language_distribution": {}
            }


# 全局会话服务实例
session_service = SessionService()
