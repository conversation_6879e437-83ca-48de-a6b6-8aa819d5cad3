"""
租户服务

提供租户的创建、查询和管理功能
"""

import logging
from datetime import datetime
from typing import Optional

from config.settings import config
from models.tenant import TenantData, TenantRequest
from storage.database_manager import database_manager
from utils.git_utils import git_utils

logger = logging.getLogger("CodeStandardMCP.TenantService")


class TenantService:
    """租户服务类"""

    def __init__(self):
        self._current_tenant = None

    async def initialize(self):
        """初始化租户服务"""
        try:
            success = await database_manager.initialize_databases()
            if success:
                logger.info("租户服务初始化成功")
                # 尝试获取或创建当前租户
                await self.get_or_create_current_tenant()
            else:
                logger.warning("租户服务初始化部分失败")
            return success
        except Exception as e:
            logger.error(f"租户服务初始化失败: {e}")
            return False

    async def get_or_create_current_tenant(self) -> TenantData:
        """获取或创建当前租户"""
        if self._current_tenant:
            return self._current_tenant

        try:
            # 获取用户信息
            user_id = str(config.get_api_user_id())
            tenant_name = git_utils.get_tenant_name()

            # 先尝试根据user_id查找现有租户
            existing_tenant = await self.get_tenant_by_user_id(user_id)
            if existing_tenant:
                self._current_tenant = existing_tenant
                logger.info(f"找到现有租户: {existing_tenant.name}")
                return existing_tenant

            # 如果不存在且允许自动创建，则创建新租户
            if config.should_auto_create_tenant():
                tenant_request = TenantRequest(
                    user_id=user_id,
                    name=tenant_name
                )
                new_tenant = await self.create_tenant(tenant_request)
                self._current_tenant = new_tenant
                logger.info(f"创建新租户: {new_tenant.name}")
                return new_tenant
            else:
                # 使用默认租户信息
                default_tenant = TenantData(
                    tenant_id=TenantData.generate_tenant_id(),
                    user_id=user_id,
                    name=config.get_default_tenant_name()
                )
                self._current_tenant = default_tenant
                logger.info(f"使用默认租户: {default_tenant.name}")
                return default_tenant

        except Exception as e:
            logger.error(f"获取或创建当前租户失败: {e}")
            # 返回默认租户
            default_tenant = TenantData(
                tenant_id=TenantData.generate_tenant_id(),
                user_id="unknown",
                name="unknown"
            )
            self._current_tenant = default_tenant
            return default_tenant

    async def create_tenant(self, tenant_request: TenantRequest) -> TenantData:
        """创建租户"""
        try:
            tenant_data = tenant_request.to_tenant_data()
            
            sql = """
            INSERT INTO tenants (tenant_id, user_id, name, created_at)
            VALUES (?, ?, ?, ?)
            """
            params = (
                tenant_data.tenant_id,
                tenant_data.user_id,
                tenant_data.name,
                tenant_data.created_at.isoformat()
            )
            
            await database_manager.execute_insert(sql, params, use_remote=False)
            logger.info(f"租户创建成功: {tenant_data.name}")
            return tenant_data
            
        except Exception as e:
            logger.error(f"创建租户失败: {e}")
            raise

    async def get_tenant_by_user_id(self, user_id: str) -> Optional[TenantData]:
        """根据用户ID获取租户"""
        try:
            sql = "SELECT * FROM tenants WHERE user_id = ? ORDER BY created_at DESC LIMIT 1"
            rows = await database_manager.execute_query(sql, (user_id,), use_remote=False)
            
            if not rows:
                return None

            row = rows[0]
            return TenantData(
                tenant_id=row['tenant_id'],
                user_id=row['user_id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at']),
                uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None
            )

        except Exception as e:
            logger.error(f"根据用户ID获取租户失败: {e}")
            return None

    async def get_tenant_by_id(self, tenant_id: str) -> Optional[TenantData]:
        """根据租户ID获取租户"""
        try:
            sql = "SELECT * FROM tenants WHERE tenant_id = ?"
            rows = await database_manager.execute_query(sql, (tenant_id,), use_remote=False)
            
            if not rows:
                return None

            row = rows[0]
            return TenantData(
                tenant_id=row['tenant_id'],
                user_id=row['user_id'],
                name=row['name'],
                created_at=datetime.fromisoformat(row['created_at']),
                uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None
            )

        except Exception as e:
            logger.error(f"根据租户ID获取租户失败: {e}")
            return None

    def get_current_tenant(self) -> Optional[TenantData]:
        """获取当前租户（同步方法）"""
        return self._current_tenant


# 全局租户服务实例
tenant_service = TenantService()
