"""
评价服务

提供评价的存储、查询和管理功能
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from models.comment import CommentData, CommentRequest
from storage.database_manager import database_manager

logger = logging.getLogger("CodeStandardMCP.CommentService")


class CommentService:
    """评价服务类"""

    def __init__(self):
        pass

    async def initialize(self):
        """初始化评价服务"""
        try:
            success = await database_manager.initialize_databases()
            if success:
                logger.info("评价服务初始化成功")
            else:
                logger.warning("评价服务初始化部分失败")
            return success
        except Exception as e:
            logger.error(f"评价服务初始化失败: {e}")
            return False

    async def create_comment(self, comment_request: CommentRequest) -> CommentData:
        """创建评价"""
        try:
            comment_data = comment_request.to_comment_data()
            await self._save_comment(comment_data)
            logger.info(f"评价创建成功: {comment_data.comment_id}")
            return comment_data
        except Exception as e:
            logger.error(f"创建评价失败: {e}")
            raise

    async def _save_comment(self, comment_data: CommentData):
        """保存评价到数据库"""
        sql = """
        INSERT INTO comments (
            comment_id, session_id, comment, created_at
        ) VALUES (?, ?, ?, ?)
        """
        params = (
            comment_data.comment_id,
            comment_data.session_id,
            comment_data.comment,
            comment_data.created_at.isoformat()
        )
        
        await database_manager.execute_insert(sql, params, use_remote=False)

    async def get_comment_by_session(self, session_id: str) -> Optional[CommentData]:
        """根据session_id获取评价"""
        try:
            sql = "SELECT * FROM comments WHERE session_id = ? ORDER BY created_at DESC LIMIT 1"
            rows = await database_manager.execute_query(sql, (session_id,), use_remote=False)
            
            if not rows:
                logger.info(f"未找到会话的评价: {session_id}")
                return None

            row = rows[0]
            return CommentData(
                comment_id=row['comment_id'],
                session_id=row['session_id'],
                comment=row['comment'],
                created_at=datetime.fromisoformat(row['created_at']),
                uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None
            )

        except Exception as e:
            logger.error(f"获取评价数据失败: {e}")
            return None

    async def get_comments_by_sessions(self, session_ids: List[str]) -> Dict[str, CommentData]:
        """根据多个session_id获取评价"""
        try:
            if not session_ids:
                return {}

            # 构建IN查询
            placeholders = ','.join('?' * len(session_ids))
            sql = f"""
            SELECT * FROM comments 
            WHERE session_id IN ({placeholders})
            ORDER BY created_at DESC
            """
            
            rows = await database_manager.execute_query(sql, tuple(session_ids), use_remote=False)
            
            comments = {}
            for row in rows:
                session_id = row['session_id']
                # 只保留每个session的最新评价
                if session_id not in comments:
                    comment = CommentData(
                        comment_id=row['comment_id'],
                        session_id=row['session_id'],
                        comment=row['comment'],
                        created_at=datetime.fromisoformat(row['created_at']),
                        uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None
                    )
                    comments[session_id] = comment
            
            return comments

        except Exception as e:
            logger.error(f"批量获取评价数据失败: {e}")
            return {}

    async def get_recent_comments(self, limit: int = 10) -> List[CommentData]:
        """获取最近的评价"""
        try:
            sql = """
            SELECT * FROM comments 
            ORDER BY created_at DESC 
            LIMIT ?
            """
            rows = await database_manager.execute_query(sql, (limit,), use_remote=False)
            
            comments = []
            for row in rows:
                comment = CommentData(
                    comment_id=row['comment_id'],
                    session_id=row['session_id'],
                    comment=row['comment'],
                    created_at=datetime.fromisoformat(row['created_at']),
                    uploaded_at=datetime.fromisoformat(row['uploaded_at']) if row['uploaded_at'] else None
                )
                comments.append(comment)
            
            return comments

        except Exception as e:
            logger.error(f"获取最近评价失败: {e}")
            return []

    async def get_comment_stats(self) -> Dict[str, Any]:
        """获取评价统计信息"""
        try:
            # 基础统计
            stats_sql = """
            SELECT 
                COUNT(*) as total_comments,
                COUNT(CASE WHEN comment IS NOT NULL AND comment != '' THEN 1 END) as comments_with_text,
                MAX(created_at) as latest_comment
            FROM comments
            """
            
            stats_result = await database_manager.execute_query(stats_sql, use_remote=False)
            stats_row = stats_result[0] if stats_result else {}

            return {
                "total_comments": stats_row.get('total_comments', 0),
                "comments_with_text": stats_row.get('comments_with_text', 0),
                "latest_comment": stats_row.get('latest_comment')
            }

        except Exception as e:
            logger.error(f"获取评价统计失败: {e}")
            return {
                "total_comments": 0,
                "comments_with_text": 0,
                "latest_comment": None
            }


# 全局评价服务实例
comment_service = CommentService()
